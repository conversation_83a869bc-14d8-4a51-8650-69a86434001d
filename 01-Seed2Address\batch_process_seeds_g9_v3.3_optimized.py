#!/usr/bin/env python3
"""
HP G9 4-CPU Performance Batch Processor - Version 3.3
=====================================================

VERSION: 3.3 4-CPU G9 Optimized (FIXED)
DATE: 2025-08-16
TARGET: HP G9 servers with 4 CPUs × 18 cores × 2 threads = 144 total threads
CPU: Intel(R) Xeon(R) CPU E7-8880 v3 @ 2.30GHz × 4

CHANGES FROM v3.2 to v3.3:
- Optimized for 4-CPU G9 configuration (144 total threads)
- Fixed worker count calculation for multi-CPU systems
- Optimized batch sizes for 4-CPU NUMA architecture
- Added CPU affinity optimization for better performance
- Reduced context switching overhead
- Optimized memory allocation per CPU socket

PERFORMANCE STRATEGY FOR 4-CPU G9:
- Use 130-140 workers (90-95% of 144 threads)
- Smaller batch sizes (2000-5000) for better CPU distribution
- NUMA-aware processing
- CPU affinity to prevent thread migration
- Memory locality optimization

EXPECTED 4-CPU G9 RESULTS:
- CPU Usage: 85-95% (vs 50-70% in v3.2)
- Speed: 150-300 seeds/second (vs 47 seeds/second in v3.2)
- Time: 30-60 seconds for 10,000 seeds (vs 211 seconds in v3.2)
"""

import sys
import argparse
import os
import time
import psutil
import threading
from multiprocessing import cpu_count, Process, Queue, Value, Pool
import csv
from typing import List, Dict, Tuple
import hashlib
import hmac
import struct
import subprocess

# Native performance libraries
try:
    import coincurve
    from coincurve import PrivateKey
    COINCURVE_AVAILABLE = True
    print("✅ coincurve (native ECDSA) available")
except ImportError:
    COINCURVE_AVAILABLE = False
    print("⚠️  coincurve not available - install with: pip install coincurve")

try:
    from cryptography.hazmat.primitives import hashes
    from cryptography.hazmat.primitives.kdf.pbkdf2 import PBKDF2HMAC
    from cryptography.hazmat.backends import default_backend
    CRYPTOGRAPHY_AVAILABLE = True
    print("✅ cryptography (native hashing) available")
except ImportError:
    CRYPTOGRAPHY_AVAILABLE = False
    print("⚠️  cryptography not available - install with: pip install cryptography")

# Fallback imports
if not COINCURVE_AVAILABLE or not CRYPTOGRAPHY_AVAILABLE:
    print("🔄 Installing required native libraries...")
    try:
        subprocess.check_call([sys.executable, "-m", "pip", "install", "coincurve", "cryptography"])
        print("✅ Native libraries installed successfully")
        # Re-import after installation
        import coincurve
        from coincurve import PrivateKey
        from cryptography.hazmat.primitives import hashes
        from cryptography.hazmat.primitives.kdf.pbkdf2 import PBKDF2HMAC
        from cryptography.hazmat.backends import default_backend
        COINCURVE_AVAILABLE = True
        CRYPTOGRAPHY_AVAILABLE = True
    except Exception as e:
        print(f"❌ Failed to install native libraries: {e}")
        print("🔄 Falling back to pure Python (will be slower)")

# Try to import tqdm for progress bars
try:
    from tqdm import tqdm
    TQDM_AVAILABLE = True
except ImportError:
    TQDM_AVAILABLE = False

# Import base58 for address encoding
try:
    import base58
except ImportError:
    print("🔄 Installing base58...")
    subprocess.check_call([sys.executable, "-m", "pip", "install", "base58"])
    import base58

# Try to import ecdsa as fallback
if not COINCURVE_AVAILABLE:
    try:
        import ecdsa
        from ecdsa.curves import SECP256k1
    except ImportError:
        print("🔄 Installing ecdsa fallback...")
        subprocess.check_call([sys.executable, "-m", "pip", "install", "ecdsa"])
        import ecdsa
        from ecdsa.curves import SECP256k1

# Constants
SECP256K1_ORDER = 0xFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFEBAAEDCE6AF48A03BBFD25E8CD0364141
HARDENED_OFFSET = 0x80000000

class NativeCrypto:
    """Native cryptographic operations for maximum G9 performance"""

    @staticmethod
    def pbkdf2_native(password: bytes, salt: bytes, iterations: int = 2048, dklen: int = 64) -> bytes:
        """Native PBKDF2 using cryptography library"""
        if CRYPTOGRAPHY_AVAILABLE:
            kdf = PBKDF2HMAC(
                algorithm=hashes.SHA512(),
                length=dklen,
                salt=salt,
                iterations=iterations,
                backend=default_backend()
            )
            return kdf.derive(password)
        else:
            # Fallback to hashlib
            return hashlib.pbkdf2_hmac('sha512', password, salt, iterations, dklen)

    @staticmethod
    def secp256k1_multiply(private_key_bytes: bytes) -> bytes:
        """Native secp256k1 point multiplication using coincurve"""
        if COINCURVE_AVAILABLE:
            private_key = PrivateKey(private_key_bytes)
            return private_key.public_key.format(compressed=True)
        else:
            # Fallback to ecdsa library
            sk = ecdsa.SigningKey.from_string(private_key_bytes, curve=SECP256k1)
            vk = sk.get_verifying_key()
            point = vk.pubkey.point
            
            # Compress public key properly
            x_bytes = point.x().to_bytes(32, 'big')
            if point.y() & 1:  # Check if y is odd
                return b'\x03' + x_bytes
            else:
                return b'\x02' + x_bytes

    @staticmethod
    def hash160_native(data: bytes) -> bytes:
        """Native hash160 (RIPEMD160(SHA256(data)))"""
        sha256_hash = hashlib.sha256(data).digest()
        try:
            ripemd160 = hashlib.new('ripemd160')
            ripemd160.update(sha256_hash)
            return ripemd160.digest()
        except ValueError:
            # Fallback: Try to use Crypto library
            try:
                from Crypto.Hash import RIPEMD160
                return RIPEMD160.new(sha256_hash).digest()
            except ImportError:
                # Use pure Python fallback
                raise NotImplementedError("RIPEMD160 not available - please install pycryptodome")

def bech32_polymod(values):
    """Bech32 checksum computation"""
    GEN = [0x3b6a57b2, 0x26508e6d, 0x1ea119fa, 0x3d4233dd, 0x2a1462b3]
    chk = 1
    for v in values:
        b = chk >> 25
        chk = (chk & 0x1ffffff) << 5 ^ v
        for i in range(5):
            chk ^= GEN[i] if ((b >> i) & 1) else 0
    return chk

def bech32_hrp_expand(hrp):
    """Expand human-readable part for Bech32"""
    return [ord(x) >> 5 for x in hrp] + [0] + [ord(x) & 31 for x in hrp]

def bech32_create_checksum(hrp, data):
    """Create Bech32 checksum"""
    values = bech32_hrp_expand(hrp) + data
    polymod = bech32_polymod(values + [0, 0, 0, 0, 0, 0]) ^ 1
    return [(polymod >> 5 * (5 - i)) & 31 for i in range(6)]

def bech32_encode(hrp, witver, witprog):
    """Encode a segwit address"""
    spec = [witver] + convertbits(witprog, 8, 5)
    if spec is None:
        return None
    checksum = bech32_create_checksum(hrp, spec)
    charset = "qpzry9x8gf2tvdw0s3jn54khce6mua7l"
    return hrp + '1' + ''.join([charset[d] for d in spec + checksum])

def convertbits(data, frombits, tobits, pad=True):
    """Convert between bit groups"""
    acc = 0
    bits = 0
    ret = []
    maxv = (1 << tobits) - 1
    max_acc = (1 << (frombits + tobits - 1)) - 1
    for value in data:
        if value < 0 or (value >> frombits):
            return None
        acc = ((acc << frombits) | value) & max_acc
        bits += frombits
        while bits >= tobits:
            bits -= tobits
            ret.append((acc >> bits) & maxv)
    if pad:
        if bits:
            ret.append((acc << (tobits - bits)) & maxv)
    elif bits >= frombits or ((acc << (tobits - bits)) & maxv):
        return None
    return ret

def derive_key_native(seed: bytes, path: str) -> Tuple[bytes, bytes]:
    """Native BIP32 key derivation"""
    hmac_result = hmac.new(b"Bitcoin seed", seed, hashlib.sha512).digest()
    master_key = hmac_result[:32]
    master_chain_code = hmac_result[32:]
    
    if not path.startswith('m/'):
        raise ValueError("Path must start with 'm/'")
    
    current_key = master_key
    current_chain_code = master_chain_code
    
    path_parts = path[2:].split('/')
    if path_parts == ['']:
        path_parts = []
    
    for part in path_parts:
        if part.endswith("'"):
            index = int(part[:-1]) + HARDENED_OFFSET
        else:
            index = int(part)
        
        if index >= HARDENED_OFFSET:
            data = b'\x00' + current_key + struct.pack('>I', index)
        else:
            parent_public_key = NativeCrypto.secp256k1_multiply(current_key)
            data = parent_public_key + struct.pack('>I', index)
        
        hmac_result = hmac.new(current_chain_code, data, hashlib.sha512).digest()
        child_key_offset = int.from_bytes(hmac_result[:32], 'big')
        parent_key_int = int.from_bytes(current_key, 'big')
        child_key_int = (child_key_offset + parent_key_int) % SECP256K1_ORDER
        
        if child_key_int == 0:
            raise ValueError("Invalid child key")
        
        current_key = child_key_int.to_bytes(32, 'big')
        current_chain_code = hmac_result[32:]
    
    public_key = NativeCrypto.secp256k1_multiply(current_key)
    return current_key, public_key

def generate_address_native(public_key: bytes, address_type: str) -> str:
    """Native address generation"""
    if address_type == "P2PKH":
        hash160 = NativeCrypto.hash160_native(public_key)
        versioned_payload = bytes([0x00]) + hash160
        checksum = hashlib.sha256(hashlib.sha256(versioned_payload).digest()).digest()[:4]
        return base58.b58encode(versioned_payload + checksum).decode('ascii')
    
    elif address_type == "P2WPKH":
        hash160 = NativeCrypto.hash160_native(public_key)
        return bech32_encode("bc", 0, hash160)
    
    elif address_type == "P2WPKH nested in P2SH":
        hash160 = NativeCrypto.hash160_native(public_key)
        witness_script = bytes([0x00, 0x14]) + hash160
        script_hash = NativeCrypto.hash160_native(witness_script)
        versioned_payload = bytes([0x05]) + script_hash
        checksum = hashlib.sha256(hashlib.sha256(versioned_payload).digest()).digest()[:4]
        return base58.b58encode(versioned_payload + checksum).decode('ascii')
    
    else:
        raise ValueError(f"Unsupported address type: {address_type}")

def validate_mnemonic_native(mnemonic: str, wordlist: List[str]) -> bool:
    """Native mnemonic validation"""
    words = mnemonic.strip().split()
    if len(words) not in [12, 15, 18, 21, 24]:
        return False
    
    try:
        indices = [wordlist.index(word) for word in words]
    except ValueError:
        return False
    
    binary_str = ''.join(format(idx, '011b') for idx in indices)
    entropy_length = len(binary_str) * 32 // 33
    entropy_bits = binary_str[:entropy_length]
    checksum_bits = binary_str[entropy_length:]
    
    entropy_bytes = bytes(int(entropy_bits[i:i+8], 2) for i in range(0, len(entropy_bits), 8))
    hash_bytes = hashlib.sha256(entropy_bytes).digest()
    expected_checksum = format(hash_bytes[0], '08b')[:len(checksum_bits)]
    
    return checksum_bits == expected_checksum
