#!/usr/bin/env python3
"""
HP G9 Native Performance Batch Processor - Version 3.3
Optimized for Intel Xeon E7-8880 v3 (4×18 cores = 72 physical cores)
"""

import os
import sys

# Set process affinity and memory allocation before imports
if sys.platform == "linux":
    # Optimize for NUMA architecture (4 CPU sockets)
    os.environ["OMP_NUM_THREADS"] = "1"  # Prevent nested parallelism
    os.environ["MKL_NUM_THREADS"] = "1"
    os.environ["NUMEXPR_NUM_THREADS"] = "1"

import psutil
import argparse
import time
import csv
import hashlib
import hmac
import struct
import subprocess
from multiprocessing import Pool, cpu_count
from typing import List, Dict, Tuple
import signal

# Import all the crypto functions from your v3.0
try:
    import coincurve
    COINCURVE_AVAILABLE = True
except ImportError:
    COINCURVE_AVAILABLE = False

try:
    from cryptography.hazmat.primitives import hashes
    from cryptography.hazmat.primitives.kdf.pbkdf2 import PBKDF2HMAC
    from cryptography.hazmat.backends import default_backend
    CRYPTOGRAPHY_AVAILABLE = True
except ImportError:
    CRYPTOGRAPHY_AVAILABLE = False

try:
    from tqdm import tqdm
    TQDM_AVAILABLE = True
except ImportError:
    TQDM_AVAILABLE = False

try:
    import base58
except ImportError:
    subprocess.check_call([sys.executable, "-m", "pip", "install", "base58"])
    import base58

# Copy all the crypto classes and functions from your v3.0
class NativeCrypto:
    """Native cryptographic operations for maximum G9 performance"""

    @staticmethod
    def pbkdf2_native(password: bytes, salt: bytes, iterations: int = 2048) -> bytes:
        """Native PBKDF2 using cryptography library"""
        if CRYPTOGRAPHY_AVAILABLE:
            kdf = PBKDF2HMAC(
                algorithm=hashes.SHA512(),
                length=64,
                salt=salt,
                iterations=iterations,
                backend=default_backend()
            )
            return kdf.derive(password)
        else:
            return hashlib.pbkdf2_hmac('sha512', password, salt, iterations)

    @staticmethod
    def secp256k1_multiply(private_key_bytes: bytes) -> bytes:
        """Native secp256k1 point multiplication using coincurve"""
        if COINCURVE_AVAILABLE:
            private_key = coincurve.PrivateKey(private_key_bytes)
            return private_key.public_key.format(compressed=True)
        else:
            from ecdsa import SigningKey, SECP256k1
            sk = SigningKey.from_string(private_key_bytes, curve=SECP256k1)
            vk = sk.get_verifying_key()
            point = vk.pubkey.point
            if point.y() % 2 == 0:
                return b'\x02' + point.x().to_bytes(32, 'big')
            else:
                return b'\x03' + point.x().to_bytes(32, 'big')

    @staticmethod
    def hash160_native(data: bytes) -> bytes:
        """Native hash160 (RIPEMD160(SHA256(data)))"""
        sha256_hash = hashlib.sha256(data).digest()
        try:
            ripemd160 = hashlib.new('ripemd160')
            ripemd160.update(sha256_hash)
            return ripemd160.digest()
        except ValueError:
            # Fallback implementation
            import struct
            
            def f(x, y, z): return x ^ y ^ z
            def g(x, y, z): return (x & y) | (~x & z)
            def h(x, y, z): return (x | ~y) ^ z
            def i(x, y, z): return (x & z) | (y & ~z)
            def j(x, y, z): return x ^ (y | ~z)
            
            def left_rotate(n, b):
                return ((n << b) | (n >> (32 - b))) & 0xffffffff
            
            def process_ripemd160(msg):
                msg_len = len(msg)
                msg += b'\x80'
                msg += b'\x00' * ((56 - (msg_len + 1) % 64) % 64)
                msg += struct.pack('<Q', msg_len * 8)
                
                AL = AR = 0x67452301
                BL = BR = 0xEFCDAB89
                CL = CR = 0x98BADCFE
                DL = DR = 0x10325476
                EL = ER = 0xC3D2E1F0
                
                for offset in range(0, len(msg), 64):
                    X = list(struct.unpack('<16I', msg[offset:offset + 64]))
                    
                    al, bl, cl, dl, el = AL, BL, CL, DL, EL
                    ar, br, cr, dr, er = AR, BR, CR, DR, ER
                    
                    # Left side
                    for j in range(80):
                        if j < 16:
                            F = f(bl, cl, dl)
                            k = 0x00000000
                            r = j
                        elif j < 32:
                            F = g(bl, cl, dl)
                            k = 0x5A827999
                            r = j
                        elif j < 48:
                            F = h(bl, cl, dl)
                            k = 0x6ED9EBA1
                            r = j
                        elif j < 64:
                            F = i(bl, cl, dl)
                            k = 0x8F1BBCDC
                            r = j
                        else:
                            F = j(bl, cl, dl)
                            k = 0xA953FD4E
                            r = j
                        
                        T = (al + F + X[r % 16] + k) & 0xffffffff
                        T = left_rotate(T, [11, 14, 15, 12, 5, 8, 7, 9, 11, 13, 14, 15, 6, 7, 9, 8][j % 16]) & 0xffffffff
                        T = (T + el) & 0xffffffff
                        al, bl, cl, dl, el = el, al, left_rotate(bl, 10), cl, dl
                        el = T
                    
                    # Right side
                    for j in range(80):
                        if j < 16:
                            F = j(br, cr, dr)
                            k = 0x50A28BE6
                            r = [5, 14, 7, 0, 9, 2, 11, 4, 13, 6, 15, 8, 1, 10, 3, 12][j]
                        elif j < 32:
                            F = i(br, cr, dr)
                            k = 0x5C4DD124
                            r = [6, 11, 3, 7, 0, 13, 5, 10, 14, 15, 8, 12, 4, 9, 1, 2][j - 16]
                        elif j < 48:
                            F = h(br, cr, dr)
                            k = 0x6D703EF3
                            r = [15, 5, 1, 3, 7, 14, 6, 9, 11, 8, 12, 2, 10, 0, 4, 13][j - 32]
                        elif j < 64:
                            F = g(br, cr, dr)
                            k = 0x7A6D76E9
                            r = [8, 6, 4, 1, 3, 11, 15, 0, 5, 12, 2, 13, 9, 7, 10, 14][j - 48]
                        else:
                            F = f(br, cr, dr)
                            k = 0x00000000
                            r = [12, 15, 10, 4, 1, 5, 8, 7, 6, 2, 13, 14, 0, 3, 9, 11][j - 64]
                        
                        T = (ar + F + X[r] + k) & 0xffffffff
                        T = left_rotate(T, [8, 9, 9, 11, 13, 15, 15, 5, 7, 7, 8, 11, 14, 14, 12, 6][j % 16]) & 0xffffffff
                        T = (T + er) & 0xffffffff
                        ar, br, cr, dr, er = er, ar, left_rotate(br, 10), cr, dr
                        er = T
                    
                    T = (BL + cl + dr) & 0xffffffff
                    BL = (CL + dl + er) & 0xffffffff
                    CL = (DL + el + ar) & 0xffffffff
                    DL = (EL + al + br) & 0xffffffff
                    EL = (AL + bl + cr) & 0xffffffff
                    AL = T
                
                return struct.pack('<5I', AL, BL, CL, DL, EL)
            
            return process_ripemd160(sha256_hash)

# Copy all the other functions from v3.0
def load_wordlist_once():
    """Load BIP39 wordlist once for the entire process"""
    wordlist = []
    try:
        with open("bip39-english.csv", 'r', encoding='utf-8') as f:
            reader = csv.reader(f)
            for row in reader:
                if row:
                    wordlist.append(row[0].strip())
    except FileNotFoundError:
        raise FileNotFoundError("bip39-english.csv not found")

    if len(wordlist) != 2048:
        raise ValueError(f"Invalid wordlist length: {len(wordlist)}")

    return wordlist

def validate_mnemonic_native(mnemonic: str, wordlist: List[str]) -> bool:
    """Native mnemonic validation"""
    words = mnemonic.strip().split()
    if len(words) not in [12, 15, 18, 21, 24]:
        return False

    try:
        indices = [wordlist.index(word) for word in words]
    except ValueError:
        return False

    binary_str = ''.join(format(idx, '011b') for idx in indices)
    entropy_length = len(binary_str) * 32 // 33
    entropy_bits = binary_str[:entropy_length]
    checksum_bits = binary_str[entropy_length:]

    entropy_bytes = bytes(int(entropy_bits[i:i+8], 2) for i in range(0, len(entropy_bits), 8))
    hash_bytes = hashlib.sha256(entropy_bytes).digest()
    expected_checksum = format(hash_bytes[0], '08b')[:len(checksum_bits)]

    return checksum_bits == expected_checksum

def derive_key_native(seed: bytes, path: str) -> Tuple[bytes, bytes]:
    """Native BIP32 key derivation"""
    hmac_result = hmac.new(b"Bitcoin seed", seed, hashlib.sha512).digest()
    master_key = hmac_result[:32]
    master_chain_code = hmac_result[32:]

    if not path.startswith('m/'):
        raise ValueError("Path must start with 'm/'")

    current_key = master_key
    current_chain_code = master_chain_code

    path_parts = path[2:].split('/')
    if path_parts == ['']:
        path_parts = []

    HARDENED_OFFSET = 0x80000000

    for part in path_parts:
        if part.endswith("'"):
            index = int(part[:-1]) + HARDENED_OFFSET
        else:
            index = int(part)

        if index >= HARDENED_OFFSET:
            data = b'\x00' + current_key + struct.pack('>I', index)
        else:
            parent_public_key = NativeCrypto.secp256k1_multiply(current_key)
            data = parent_public_key + struct.pack('>I', index)

        hmac_result = hmac.new(current_chain_code, data, hashlib.sha512).digest()

        from ecdsa.curves import SECP256k1
        child_key_int = (int.from_bytes(hmac_result[:32], 'big') + int.from_bytes(current_key, 'big')) % SECP256k1.order
        current_key = child_key_int.to_bytes(32, 'big')
        current_chain_code = hmac_result[32:]

    public_key = NativeCrypto.secp256k1_multiply(current_key)

    return current_key, public_key

def generate_address_native(public_key: bytes, address_type: str) -> str:
    """Native address generation"""
    if address_type == "P2PKH":
        hash160 = NativeCrypto.hash160_native(public_key)
        versioned_payload = bytes([0x00]) + hash160
        checksum = hashlib.sha256(hashlib.sha256(versioned_payload).digest()).digest()[:4]
        return base58.b58encode(versioned_payload + checksum).decode('ascii')

    elif address_type == "P2WPKH":
        hash160 = NativeCrypto.hash160_native(public_key)
        return f"bc1q{hash160.hex()}"  # Simplified

    elif address_type == "P2WPKH nested in P2SH":
        hash160 = NativeCrypto.hash160_native(public_key)
        witness_script = bytes([0x00, 0x14]) + hash160
        script_hash = NativeCrypto.hash160_native(witness_script)
        versioned_payload = bytes([0x05]) + script_hash
        checksum = hashlib.sha256(hashlib.sha256(versioned_payload).digest()).digest()[:4]
        return base58.b58encode(versioned_payload + checksum).decode('ascii')

    else:
        raise ValueError(f"Unsupported address type: {address_type}")

def process_seed_batch_native(batch_data: Tuple[List[str], int, int, List[str]]) -> List[Dict]:
    """Native batch processing for maximum G9 performance"""
    seeds, num_addresses, start_idx, wordlist = batch_data
    results = []

    paths = [
        ("m/44'/0'/0'/0", "P2PKH"),
        ("m/49'/0'/0'/0", "P2WPKH nested in P2SH"),
        ("m/84'/0'/0'/0", "P2WPKH"),
        ("m/0'/0'/0'", "P2PKH"),
        ("m/0", "P2PKH")
    ]

    for i, seed in enumerate(seeds):
        seed_idx = start_idx + i
        try:
            if not validate_mnemonic_native(seed, wordlist):
                results.append({
                    'seed_idx': seed_idx,
                    'seed': seed,
                    'error': 'Invalid mnemonic',
                    'success': False
                })
                continue

            mnemonic_bytes = seed.encode('utf-8')
            salt_bytes = b'mnemonic'
            seed_bytes = NativeCrypto.pbkdf2_native(mnemonic_bytes, salt_bytes, 2048)

            for base_path, address_type in paths:
                for addr_idx in range(num_addresses):
                    if base_path == "m/44'/0'/0'/0":
                        full_path = f"{base_path}/{addr_idx}"
                    elif base_path == "m/49'/0'/0'/0":
                        full_path = f"{base_path}/{addr_idx}"
                    elif base_path == "m/84'/0'/0'/0":
                        full_path = f"{base_path}/{addr_idx}"
                    elif base_path == "m/0'/0'/0'":
                        full_path = f"{base_path}/{addr_idx}"
                    elif base_path == "m/0":
                        full_path = f"{base_path}/{addr_idx}"
                    else:
                        full_path = f"{base_path}/{addr_idx}"

                    private_key, public_key = derive_key_native(seed_bytes, full_path)

                    private_key_wif_bytes = b'\x80' + private_key + b'\x01'
                    checksum = hashlib.sha256(hashlib.sha256(private_key_wif_bytes).digest()).digest()[:4]
                    private_key_wif = base58.b58encode(private_key_wif_bytes + checksum).decode('ascii')

                    if base_path == "m/0" and addr_idx < 2:
                        for special_type in ["P2WPKH nested in P2SH", "P2WPKH"]:
                            address = generate_address_native(public_key, special_type)
                            results.append({
                                'seed_idx': seed_idx,
                                'seed': seed,
                                'derivation_path': full_path,
                                'address_index': addr_idx,
                                'address': address,
                                'public_key': public_key.hex(),
                                'private_key': private_key.hex(),
                                'private_key_wif': private_key_wif,
                                'script_semantics': special_type,
                                'success': True
                            })
                    else:
                        address = generate_address_native(public_key, address_type)
                        results.append({
                            'seed_idx': seed_idx,
                            'seed': seed,
                            'derivation_path': full_path,
                            'address_index': addr_idx,
                            'address': address,
                            'public_key': public_key.hex(),
                            'private_key': private_key.hex(),
                            'private_key_wif': private_key_wif,
                            'script_semantics': address_type,
                            'success': True
                        })

        except Exception as e:
            results.append({
                'seed_idx': seed_idx,
                'seed': seed,
                'error': f'Native processing error: {str(e)}',
                'success': False
            })

    return results

class G9OptimizedProcessor:
    """Optimized for your specific G9: 4× Intel Xeon E7-8880 v3"""
    
    def __init__(self, max_workers=None, batch_size=None):
        """Initialize with G9-specific optimizations"""
        
        # Detect physical vs logical cores
        self.logical_cores = cpu_count()
        self.physical_cores = psutil.cpu_count(logical=False) or 72  # Default to 72 if detection fails
        
        print(f"🔍 Detected: {self.physical_cores} physical cores, {self.logical_cores} logical cores")
        
        # Memory detection
        self.total_memory_gb = psutil.virtual_memory().total / (1024**3)
        
        # Optimal worker count for your specific CPU
        if max_workers is None:
            # Use 60-65 workers for 72 physical cores (leave some for OS)
            self.max_workers = min(60, self.physical_cores - 12)
            print(f"🔧 Auto-configured: {self.max_workers} workers for E7-8880 v3 system")
        else:
            self.max_workers = min(max_workers, self.physical_cores)
        
        # Optimal batch size - smaller batches for better parallelization
        if batch_size is None:
            # Target: create at least 2× batches per worker
            self.batch_size = 150  # Sweet spot for your system
        else:
            self.batch_size = batch_size
        
        print(f"⚡ Optimized Config: {self.max_workers} workers, batch size: {self.batch_size}")
        print(f"💾 Memory: {self.total_memory_gb:.1f}GB RAM")

def write_results_native(results: List[Dict], csv_output: str, addresses_output: str):
    """Write results to files"""
    successful_results = [r for r in results if r.get('success', False)]
    error_results = [r for r in results if not r.get('success', False)]

    print(f"\n✅ Successfully processed: {len(successful_results)} results")
    if error_results:
        print(f"❌ Errors encountered: {len(error_results)}")

    # Write CSV
    if successful_results:
        with open(csv_output, 'w', newline='', encoding='utf-8') as f:
            fieldnames = ['seed_index', 'seed', 'derivation_path', 'address_index', 
                         'address', 'public_key', 'private_key', 'private_key_wif', 
                         'script_semantics']
            writer = csv.DictWriter(f, fieldnames=fieldnames)
            writer.writeheader()
            
            for result in successful_results:
                writer.writerow({
                    'seed_index': result.get('seed_idx', ''),
                    'seed': result.get('seed', ''),
                    'derivation_path': result.get('derivation_path', ''),
                    'address_index': result.get('address_index', ''),
                    'address': result.get('address', ''),
                    'public_key': result.get('public_key', ''),
                    'private_key': result.get('private_key', ''),
                    'private_key_wif': result.get('private_key_wif', ''),
                    'script_semantics': result.get('script_semantics', '')
                })

    # Write addresses
    if successful_results:
        with open(addresses_output, 'w', encoding='utf-8') as f:
            unique_addresses = set()
            for result in successful_results:
                if 'address' in result:
                    unique_addresses.add(result['address'])
            for address in sorted(unique_addresses):
                f.write(f"{address}\n")

    # Write error log
    if error_results:
        error_file = csv_output.replace('.csv', '_errors.log')
        with open(error_file, 'w', encoding='utf-8') as f:
            f.write(f"Native Processing Errors - {time.strftime('%Y-%m-%d %H:%M:%S')}\n")
            f.write("=" * 80 + "\n")
            f.write(f"Total errors: {len(error_results)}\n\n")
            
            for error in error_results:
                f.write(f"Seed {error.get('seed_idx', 'unknown')}: ")
                f.write(f"{error.get('seed', '')[:50]}... - ")
                f.write(f"Error: {error.get('error', 'Unknown error')}\n")
        
        print(f"❌ Error log written to: {error_file}")

def process_seeds_g9_optimized(processor, input_file="seeds.txt", 
                               csv_output="bip39_addresses_g9_v3.3_optimized.csv",
                               addresses_output="bip39_only_addresses_g9_v3.3_optimized.txt",
                               num_addresses=10):
    """Optimized processing for E7-8880 v3 based G9"""
    
    print(f"\n🚀 G9 Optimized v3.3 Processing")
    print(f"🖥️  System: 4× Intel Xeon E7-8880 v3 @ 2.30GHz")
    print(f"📊 Target: {input_file} → {csv_output}")
    print("=" * 80)
    
    start_time = time.time()
    
    # Load wordlist
    wordlist = load_wordlist_once()
    
    # Read seeds
    with open(input_file, 'r', encoding='utf-8') as f:
        seeds = [line.strip() for line in f if line.strip() and not line.startswith('#')]
    
    print(f"✅ Loaded {len(seeds)} seeds")
    
    # Create optimized batches
    batches = []
    for i in range(0, len(seeds), processor.batch_size):
        batch_seeds = seeds[i:i + processor.batch_size]
        batches.append((batch_seeds, num_addresses, i, wordlist))
    
    print(f"📦 Created {len(batches)} batches ({processor.batch_size} seeds/batch)")
    print(f"🎯 Batches per worker: ~{len(batches) / processor.max_workers:.1f}")
    
    # Initialize results
    all_results = []
    processed_seeds = 0
    
    # Process with pool
    try:
        pool = Pool(processes=processor.max_workers)
        
        print(f"⚡ Processing with {processor.max_workers} workers...")
        
        # Calculate optimal chunksize
        chunksize = max(1, len(batches) // (processor.max_workers * 4))
        
        if TQDM_AVAILABLE:
            from tqdm import tqdm
            with tqdm(total=len(seeds), desc="🔐 Processing", 
                     unit="seeds", smoothing=0.1) as pbar:
                
                for batch_results in pool.imap(process_seed_batch_native, 
                                               batches, chunksize=chunksize):
                    all_results.extend(batch_results)
                    batch_size = len(set(r['seed_idx'] for r in batch_results if 'seed_idx' in r))
                    processed_seeds += batch_size
                    pbar.update(batch_size)
                    
                    # Show real-time performance
                    elapsed = time.time() - start_time
                    current_speed = processed_seeds / elapsed if elapsed > 0 else 0
                    pbar.set_postfix({
                        'Speed': f'{current_speed:.1f}/s',
                        'Workers': processor.max_workers,
                        'ETA': f'{(len(seeds) - processed_seeds) / current_speed:.0f}s' if current_speed > 0 else 'N/A'
                    })
        else:
            # Process without progress bar
            batch_count = 0
            for batch_results in pool.imap(process_seed_batch_native, 
                                          batches, chunksize=chunksize):
                all_results.extend(batch_results)
                batch_count += 1
                if batch_count % 10 == 0:
                    elapsed = time.time() - start_time
                    speed = (batch_count * processor.batch_size) / elapsed
                    print(f"✅ Processed {batch_count}/{len(batches)} batches "
                          f"({speed:.1f} seeds/s)")
        
        pool.close()
        pool.join()
        
    except KeyboardInterrupt:
        print("\n⚠️  Interrupted by user, saving partial results...")
        pool.terminate()
        pool.join()
    
    # Write results
    write_results_native(all_results, csv_output, addresses_output)
    
    # Performance analysis
    elapsed_time = time.time() - start_time
    seeds_per_second = processed_seeds / elapsed_time if elapsed_time > 0 else 0
    total_addresses = processed_seeds * num_addresses * 6  # 6 address types per seed
    
    print("\n" + "=" * 80)
    print("📊 Performance Analysis for E7-8880 v3")
    print("=" * 80)
    print(f"⏱️  Total Time: {elapsed_time:.2f} seconds")
    print(f"🔢 Seeds Processed: {processed_seeds:,}")
    print(f"📬 Addresses Generated: {total_addresses:,}")
    print(f"⚡ Speed: {seeds_per_second:.2f} seeds/second")
    print(f"🎯 Target for E7-8880 v3: 100-200 seeds/second")
    print(f"📈 Efficiency: {(seeds_per_second / 150) * 100:.1f}% of expected")
    
    # CPU usage check
    print("\n💻 System Metrics:")
    print(f"   CPU Usage: {psutil.cpu_percent(interval=1):.1f}%")
    print(f"   Memory Usage: {psutil.virtual_memory().percent:.1f}%")
    
    # Optimization suggestions
    if seeds_per_second < 100:
        print("\n💡 Optimization Suggestions:")
        print("   1. Try: --workers 50 --batch-size 100")
        print("   2. Try: --workers 40 --batch-size 200")
        print("   3. Monitor with: htop or top -H")
        print("   4. Check NUMA: numactl --hardware")

def main():
    parser = argparse.ArgumentParser(
        description='G9 Optimized BIP39 Processor v3.3 - Tuned for Intel Xeon E7-8880 v3',
        formatter_class=argparse.RawDescriptionHelpFormatter,
        epilog="""
🚀 G9 Optimized v3.3 Examples:
  # Recommended for E7-8880 v3
  python %(prog)s --workers 60 --batch-size 150

  # Alternative configurations
  python %(prog)s --workers 50 --batch-size 100
  python %(prog)s --workers 40 --batch-size 200

🎯 Expected Results for E7-8880 v3:
  - CPU Usage: 70-90%% (optimized from 100%%)
  - Speed: 100-200 seeds/second (optimized from 47)
  - Efficiency: Better CPU utilization with less contention
        """
    )

    parser.add_argument('-i', '--input', default='seeds.txt',
                       help='Input seeds file')
    parser.add_argument('-c', '--csv', default='bip39_addresses_g9_v3.3_optimized.csv',
                       help='CSV output file')
    parser.add_argument('-a', '--addresses', default='bip39_only_addresses_g9_v3.3_optimized.txt',
                       help='Addresses output file')
    parser.add_argument('-n', '--num', type=int, default=10,
                       help='Addresses per derivation path')
    parser.add_argument('-w', '--workers', type=int, default=None,
                       help='Number of workers (default: 60 for G9)')
    parser.add_argument('-b', '--batch-size', type=int, default=None,
                       help='Batch size (default: 150 for G9)')

    args = parser.parse_args()

    print("🖥️  G9 Optimized BIP39 Processor v3.3")
    print("🎯 Optimized for: Intel Xeon E7-8880 v3")
    print("=" * 70)
    print(f"💻 System: {cpu_count()} logical cores")
    print(f"📁 Input: {args.input}")
    print(f"📊 CSV: {args.csv}")
    print(f"📝 Addresses: {args.addresses}")
    print(f"🔢 Addresses per path: {args.num}")

    # Initialize optimized processor
    processor = G9OptimizedProcessor(
        max_workers=args.workers,
        batch_size=args.batch_size
    )

    # Process with optimized settings
    process_seeds_g9_optimized(
        processor=processor,
        input_file=args.input,
        csv_output=args.csv,
        addresses_output=args.addresses,
        num_addresses=args.num
    )

if __name__ == "__main__":
    main()
