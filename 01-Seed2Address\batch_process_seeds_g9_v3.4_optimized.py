#!/usr/bin/env python3
"""
HP G9 Native Performance Batch Processor - Version 3.4
Fixed BIP32 derivation and address generation
"""

import os
import sys
import hashlib
import hmac
import struct
import csv
import time
import argparse
from multiprocessing import Pool, cpu_count
from typing import List, Dict, Tuple
import subprocess

# Try imports
try:
    import base58
except ImportError:
    subprocess.check_call([sys.executable, "-m", "pip", "install", "base58"])
    import base58

try:
    from ecdsa import SigningKey, SECP256k1
    from ecdsa.curves import SECP256k1 as SECP256K1_CURVE
    from ecdsa.ellipticcurve import INFINITY
    ECDSA_AVAILABLE = True
except ImportError:
    subprocess.check_call([sys.executable, "-m", "pip", "install", "ecdsa"])
    from ecdsa import SigningKey, SECP256k1
    from ecdsa.curves import SECP256k1 as SECP256K1_CURVE
    from ecdsa.ellipticcurve import INFINITY
    ECDSA_AVAILABLE = True

# Bech32 encoding functions
CHARSET = "qpzry9x8gf2tvdw0s3jn54khce6mua7l"

def bech32_polymod(values):
    """Internal function for Bech32 checksum."""
    generator = [0x3b6a57b2, 0x26508e6d, 0x1ea119fa, 0x3d4233dd, 0x2a1462b3]
    chk = 1
    for value in values:
        top = chk >> 25
        chk = (chk & 0x1ffffff) << 5 ^ value
        for i in range(5):
            chk ^= generator[i] if ((top >> i) & 1) else 0
    return chk

def bech32_hrp_expand(hrp):
    """Expand the HRP into values."""
    return [ord(x) >> 5 for x in hrp] + [0] + [ord(x) & 31 for x in hrp]

def bech32_verify_checksum(hrp, data):
    """Verify a checksum given HRP and converted data characters."""
    return bech32_polymod(bech32_hrp_expand(hrp) + data) == 1

def bech32_create_checksum(hrp, data):
    """Compute the checksum values given HRP and data."""
    values = bech32_hrp_expand(hrp) + data
    polymod = bech32_polymod(values + [0, 0, 0, 0, 0, 0]) ^ 1
    return [(polymod >> 5 * (5 - i)) & 31 for i in range(6)]

def bech32_encode(hrp, data):
    """Encode a segwit address."""
    combined = data + bech32_create_checksum(hrp, data)
    return hrp + '1' + ''.join([CHARSET[d] for d in combined])

def convertbits(data, frombits, tobits, pad=True):
    """General power-of-2 base conversion."""
    acc = 0
    bits = 0
    ret = []
    maxv = (1 << tobits) - 1
    max_acc = (1 << (frombits + tobits - 1)) - 1
    for value in data:
        if value < 0 or (value >> frombits):
            return None
        acc = ((acc << frombits) | value) & max_acc
        bits += frombits
        while bits >= tobits:
            bits -= tobits
            ret.append((acc >> bits) & maxv)
    if pad:
        if bits:
            ret.append((acc << (tobits - bits)) & maxv)
    elif bits >= frombits or ((acc << (tobits - bits)) & maxv):
        return None
    return ret

def hash160(data: bytes) -> bytes:
    """RIPEMD160(SHA256(data))"""
    sha256_hash = hashlib.sha256(data).digest()
    try:
        h = hashlib.new('ripemd160')
        h.update(sha256_hash)
        return h.digest()
    except ValueError:
        # Fallback pure Python RIPEMD160 implementation
        import struct
        
        def f(x, y, z): return x ^ y ^ z
        def g(x, y, z): return (x & y) | (~x & z)
        def h(x, y, z): return (x | ~y) ^ z
        def i(x, y, z): return (x & z) | (y & ~z)
        def j(x, y, z): return x ^ (y | ~z)
        
        def left_rotate(n, b):
            return ((n << b) | (n >> (32 - b))) & 0xffffffff
        
        def ripemd160(msg):
            msg_len = len(msg)
            msg += b'\x80'
            msg += b'\x00' * ((56 - (msg_len + 1) % 64) % 64)
            msg += struct.pack('<Q', msg_len * 8)
            
            AL = AR = 0x67452301
            BL = BR = 0xEFCDAB89
            CL = CR = 0x98BADCFE
            DL = DR = 0x10325476
            EL = ER = 0xC3D2E1F0
            
            zl = [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15,
                  7, 4, 13, 1, 10, 6, 15, 3, 12, 0, 9, 5, 2, 14, 11, 8,
                  3, 10, 14, 4, 9, 15, 8, 1, 2, 7, 0, 6, 13, 11, 5, 12,
                  1, 9, 11, 10, 0, 8, 12, 4, 13, 3, 7, 15, 14, 5, 6, 2,
                  4, 0, 5, 9, 7, 12, 2, 10, 14, 1, 3, 8, 11, 6, 15, 13]
            
            zr = [5, 14, 7, 0, 9, 2, 11, 4, 13, 6, 15, 8, 1, 10, 3, 12,
                  6, 11, 3, 7, 0, 13, 5, 10, 14, 15, 8, 12, 4, 9, 1, 2,
                  15, 5, 1, 3, 7, 14, 6, 9, 11, 8, 12, 2, 10, 0, 4, 13,
                  8, 6, 4, 1, 3, 11, 15, 0, 5, 12, 2, 13, 9, 7, 10, 14,
                  12, 15, 10, 4, 1, 5, 8, 7, 6, 2, 13, 14, 0, 3, 9, 11]
            
            sl = [11, 14, 15, 12, 5, 8, 7, 9, 11, 13, 14, 15, 6, 7, 9, 8,
                  7, 6, 8, 13, 11, 9, 7, 15, 7, 12, 15, 9, 11, 7, 13, 12,
                  11, 13, 6, 7, 14, 9, 13, 15, 14, 8, 13, 6, 5, 12, 7, 5,
                  11, 12, 14, 15, 14, 15, 9, 8, 9, 14, 5, 6, 8, 6, 5, 12,
                  9, 15, 5, 11, 6, 8, 13, 12, 5, 12, 13, 14, 11, 8, 5, 6]
            
            sr = [8, 9, 9, 11, 13, 15, 15, 5, 7, 7, 8, 11, 14, 14, 12, 6,
                  9, 13, 15, 7, 12, 8, 9, 11, 7, 7, 12, 7, 6, 15, 13, 11,
                  9, 7, 15, 11, 8, 6, 6, 14, 12, 13, 5, 14, 13, 13, 7, 5,
                  15, 5, 8, 11, 14, 14, 6, 14, 6, 9, 12, 9, 12, 5, 15, 8,
                  8, 5, 12, 9, 12, 5, 14, 6, 8, 13, 6, 5, 15, 13, 11, 11]
            
            for offset in range(0, len(msg), 64):
                X = list(struct.unpack('<16I', msg[offset:offset + 64]))
                
                al, bl, cl, dl, el = AL, BL, CL, DL, EL
                ar, br, cr, dr, er = AR, BR, CR, DR, ER
                
                for j in range(80):
                    t = al + f(bl, cl, dl) + X[zl[j]] + 0x00000000
                    if 16 <= j < 32:
                        t = al + g(bl, cl, dl) + X[zl[j]] + 0x5A827999
                    elif 32 <= j < 48:
                        t = al + h(bl, cl, dl) + X[zl[j]] + 0x6ED9EBA1
                    elif 48 <= j < 64:
                        t = al + i(bl, cl, dl) + X[zl[j]] + 0x8F1BBCDC
                    elif 64 <= j:
                        t = al + j(bl, cl, dl) + X[zl[j]] + 0xA953FD4E
                    
                    t = (t + el) & 0xffffffff
                    t = left_rotate(t, sl[j]) & 0xffffffff
                    t = (t + cl) & 0xffffffff
                    al = el
                    el = dl
                    dl = left_rotate(cl, 10)
                    cl = bl
                    bl = t
                    
                    t = ar + j(br, cr, dr) + X[zr[j]] + 0x50A28BE6
                    if 16 <= j < 32:
                        t = ar + i(br, cr, dr) + X[zr[j]] + 0x5C4DD124
                    elif 32 <= j < 48:
                        t = ar + h(br, cr, dr) + X[zr[j]] + 0x6D703EF3
                    elif 48 <= j < 64:
                        t = ar + g(br, cr, dr) + X[zr[j]] + 0x7A6D76E9
                    elif 64 <= j:
                        t = ar + f(br, cr, dr) + X[zr[j]] + 0x00000000
                    
                    t = (t + er) & 0xffffffff
                    t = left_rotate(t, sr[j]) & 0xffffffff
                    t = (t + cr) & 0xffffffff
                    ar = er
                    er = dr
                    dr = left_rotate(cr, 10)
                    cr = br
                    br = t
                
                t = (BL + cl + dr) & 0xffffffff
                BL = (CL + dl + er) & 0xffffffff
                CL = (DL + el + ar) & 0xffffffff
                DL = (EL + al + br) & 0xffffffff
                EL = (AL + bl + cr) & 0xffffffff
                AL = t
                
                AR, BR, CR, DR, ER = AL, BL, CL, DL, EL
            
            return struct.pack('<5I', AL, BL, CL, DL, EL)
        
        return ripemd160(sha256_hash)

def privkey_to_pubkey(privkey_bytes: bytes, compressed: bool = True) -> bytes:
    """Convert private key to public key"""
    sk = SigningKey.from_string(privkey_bytes, curve=SECP256k1)
    vk = sk.get_verifying_key()
    
    if compressed:
        x = vk.pubkey.point.x()
        y = vk.pubkey.point.y()
        if y % 2 == 0:
            return b'\x02' + x.to_bytes(32, 'big')
        else:
            return b'\x03' + x.to_bytes(32, 'big')
    else:
        return b'\x04' + vk.to_string()

def derive_bip32_privkey(seed: bytes, path: str) -> Tuple[bytes, bytes]:
    """
    BIP32 HD key derivation
    Returns (private_key, chain_code)
    """
    # Parse path
    if not path.startswith('m'):
        raise ValueError("Path must start with 'm'")
    
    path = path.strip('/')
    
    # Generate master key from seed
    hmac_result = hmac.new(b"Bitcoin seed", seed, hashlib.sha512).digest()
    master_private_key = hmac_result[:32]
    master_chain_code = hmac_result[32:]
    
    # Check if master key is valid
    if int.from_bytes(master_private_key, 'big') == 0 or int.from_bytes(master_private_key, 'big') >= SECP256K1_CURVE.order:
        raise ValueError("Invalid master key")
    
    # Start with master
    current_key = master_private_key
    current_chain = master_chain_code
    
    # Process path components
    components = path.split('/')[1:]  # Skip 'm'
    if components == ['']:  # path was just 'm'
        components = []
    
    for component in components:
        # Check if hardened
        if component.endswith("'"):
            index = int(component[:-1]) + 0x80000000
        else:
            index = int(component)
        
        # Derive child key
        if index >= 0x80000000:  # Hardened
            # data = 0x00 || ser256(kpar) || ser32(i)
            data = b'\x00' + current_key + struct.pack('>I', index)
        else:  # Non-hardened
            # data = serP(point(kpar)) || ser32(i)
            public_key = privkey_to_pubkey(current_key, compressed=True)
            data = public_key + struct.pack('>I', index)
        
        # Generate child key
        hmac_result = hmac.new(current_chain, data, hashlib.sha512).digest()
        child_key_offset = hmac_result[:32]
        child_chain = hmac_result[32:]
        
        # Calculate child private key
        child_key_int = (int.from_bytes(child_key_offset, 'big') + int.from_bytes(current_key, 'big')) % SECP256K1_CURVE.order
        
        # Check for invalid key
        if child_key_int == 0:
            raise ValueError("Invalid child key")
        
        current_key = child_key_int.to_bytes(32, 'big')
        current_chain = child_chain
    
    return current_key, current_chain

def create_wif(private_key: bytes, compressed: bool = True, testnet: bool = False) -> str:
    """Create Wallet Import Format (WIF) from private key"""
    if testnet:
        version = b'\xef'
    else:
        version = b'\x80'
    
    if compressed:
        extended = version + private_key + b'\x01'
    else:
        extended = version + private_key
    
    checksum = hashlib.sha256(hashlib.sha256(extended).digest()).digest()[:4]
    return base58.b58encode(extended + checksum).decode('ascii')

def pubkey_to_p2pkh_address(pubkey: bytes, testnet: bool = False) -> str:
    """Convert public key to P2PKH address"""
    pubkey_hash = hash160(pubkey)
    
    if testnet:
        version = b'\x6f'
    else:
        version = b'\x00'
    
    versioned = version + pubkey_hash
    checksum = hashlib.sha256(hashlib.sha256(versioned).digest()).digest()[:4]
    return base58.b58encode(versioned + checksum).decode('ascii')

def pubkey_to_p2wpkh_address(pubkey: bytes, testnet: bool = False) -> str:
    """Convert public key to native P2WPKH bech32 address"""
    pubkey_hash = hash160(pubkey)
    
    # witness version (0) + hash160
    witness_program = convertbits([0] + list(pubkey_hash), 8, 5)
    
    if testnet:
        hrp = "tb"
    else:
        hrp = "bc"
    
    return bech32_encode(hrp, witness_program)

def pubkey_to_p2sh_p2wpkh_address(pubkey: bytes, testnet: bool = False) -> str:
    """Convert public key to P2WPKH wrapped in P2SH address"""
    pubkey_hash = hash160(pubkey)
    
    # Build witness script: OP_0 + push(hash160)
    witness_script = b'\x00\x14' + pubkey_hash
    
    # P2SH address from witness script
    script_hash = hash160(witness_script)
    
    if testnet:
        version = b'\xc4'
    else:
        version = b'\x05'
    
    versioned = version + script_hash
    checksum = hashlib.sha256(hashlib.sha256(versioned).digest()).digest()[:4]
    return base58.b58encode(versioned + checksum).decode('ascii')

def process_seed(mnemonic: str, wordlist: List[str]) -> Dict:
    """Process a single mnemonic seed and generate all required outputs"""
    results = {}
    
    # Generate seed from mnemonic
    mnemonic_bytes = mnemonic.encode('utf-8')
    passphrase_bytes = b'mnemonic'  # Standard BIP39 passphrase prefix
    seed = hashlib.pbkdf2_hmac('sha512', mnemonic_bytes, passphrase_bytes, 2048, 64)
    
    results['mnemonic'] = mnemonic
    results['seed'] = seed.hex()
    results['addresses'] = []
    
    # Define all derivation paths
    paths = [
        # BIP32 paths
        ("m/0'/0'/0'", "BIP32"),
        ("m/0'/0'/1'", "BIP32"),
        
        # BIP44 paths
        ("m/44'/0'/0'/0/0", "BIP44"),
        ("m/44'/0'/0'/0/1", "BIP44"),
        
        # BIP49 paths
        ("m/49'/0'/0'/0/0", "BIP49"),
        ("m/49'/0'/0'/0/1", "BIP49"),
        
        # BIP84 paths
        ("m/84'/0'/0'/0/0", "BIP84"),
        ("m/84'/0'/0'/0/1", "BIP84"),
        
        # BIP141 paths (special handling)
        ("m/0/0", "BIP141-P2SH"),
        ("m/0/1", "BIP141-P2SH"),
        ("m/0/0", "BIP141-P2WPKH"),
        ("m/0/1", "BIP141-P2WPKH"),
    ]
    
    for path, path_type in paths:
        try:
            # Derive private key
            private_key, _ = derive_bip32_privkey(seed, path)
            
            # Get public key
            public_key = privkey_to_pubkey(private_key, compressed=True)
            
            # Generate WIF
            wif = create_wif(private_key, compressed=True)
            
            # Generate address based on path type
            if path_type == "BIP32":
                address = pubkey_to_p2pkh_address(public_key)
                script_type = "P2PKH"
            elif path_type == "BIP44":
                address = pubkey_to_p2pkh_address(public_key)
                script_type = "P2PKH"
            elif path_type == "BIP49":
                address = pubkey_to_p2sh_p2wpkh_address(public_key)
                script_type = "P2WPKH nested in P2SH"
            elif path_type == "BIP84":
                address = pubkey_to_p2wpkh_address(public_key)
                script_type = "P2WPKH"
            elif path_type == "BIP141-P2SH":
                address = pubkey_to_p2sh_p2wpkh_address(public_key)
                script_type = "P2WPKH nested in P2SH"
            elif path_type == "BIP141-P2WPKH":
                address = pubkey_to_p2wpkh_address(public_key)
                script_type = "P2WPKH"
            
            results['addresses'].append({
                'path': path,
                'address': address,
                'public_key': public_key.hex(),
                'private_key': private_key.hex(),
                'wif': wif,
                'script_type': script_type
            })
            
        except Exception as e:
            print(f"Error processing path {path}: {str(e)}")
            continue
    
    return results

def main():
    # Test with your mnemonic
    mnemonic = "motor venture dilemma quote subject magnet keep large dry gossip bean paper"
    wordlist = []  # Not needed for this test
    
    print("Processing mnemonic...")
    results = process_seed(mnemonic, wordlist)
    
    print(f"\nBIP39 Mnemonic: {results['mnemonic']}")
    print(f"\nBIP39 Seed: {results['seed']}")
    print(f"\nCoin: BTC")
    print("\nDerivation Path and outputs\n")
    print("path,address,public key,private key")
    
    for addr_info in results['addresses']:
        print(f"{addr_info['path']},{addr_info['address']},{addr_info['public_key']},{addr_info['wif']}")

if __name__ == "__main__":
    main()
